/**
 * Klicktape User Profile Redis Caching Implementation
 * Reduces Supabase costs and improves performance for user profiles and avatars
 */

import { Redis } from '@upstash/redis';

// Initialize Redis client
const redis = new Redis({
  url: process.env.EXPO_PUBLIC_UPSTASH_REDIS_REST_URL!,
  token: process.env.EXPO_PUBLIC_UPSTASH_REDIS_REST_TOKEN!,
});

// Cache key prefixes
const CACHE_KEYS = {
  USER_PROFILE: 'user:profile:',
  USER_AVATAR: 'user:avatar:',
  USER_BASIC: 'user:basic:',
  USER_BATCH: 'user:batch:',
} as const;

// Cache TTL (Time To Live) in seconds
const CACHE_TTL = {
  USER_PROFILE: 1800, // 30 minutes
  USER_AVATAR: 3600, // 1 hour (avatars change less frequently)
  USER_BASIC: 900, // 15 minutes
  USER_BATCH: 600, // 10 minutes
} as const;

// Types
interface CachedUserProfile {
  id: string;
  username: string;
  avatar_url: string | null;
  name?: string;
  bio?: string;
  account_type?: string;
  gender?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

interface CachedUserBasic {
  id: string;
  username: string;
  avatar_url: string | null;
}

/**
 * User Profile Cache Manager
 */
export class UserProfileCache {
  /**
   * Get user profile from cache or fallback to database
   */
  static async getUserProfile(
    userId: string,
    fallbackFn?: () => Promise<CachedUserProfile | null>
  ): Promise<CachedUserProfile | null> {
    try {
      const cacheKey = `${CACHE_KEYS.USER_PROFILE}${userId}`;
      
      // Try to get from cache first
      const cached = await redis.get<CachedUserProfile>(cacheKey);
      
      if (cached) {
        console.log(`📱 User profile for ${userId} served from cache`);
        return cached;
      }
      
      if (fallbackFn) {
        console.log(`🔄 User profile cache miss for ${userId}, fetching from database`);
        const freshData = await fallbackFn();
        
        if (freshData) {
          await this.setUserProfile(userId, freshData);
        }
        
        return freshData;
      }
      
      return null;
    } catch (error) {
      console.error(`❌ Error getting user profile from cache for ${userId}:`, error);
      return fallbackFn ? await fallbackFn() : null;
    }
  }

  /**
   * Cache user profile data
   */
  static async setUserProfile(userId: string, profile: CachedUserProfile): Promise<void> {
    try {
      const cacheKey = `${CACHE_KEYS.USER_PROFILE}${userId}`;
      await redis.setex(cacheKey, CACHE_TTL.USER_PROFILE, profile);
      
      // Also cache basic user info for comments
      const basicInfo: CachedUserBasic = {
        id: profile.id,
        username: profile.username,
        avatar_url: profile.avatar_url,
      };
      await this.setUserBasic(userId, basicInfo);
      
      console.log(`✅ User profile cached for ${userId}`);
    } catch (error) {
      console.error(`❌ Error caching user profile for ${userId}:`, error);
    }
  }

  /**
   * Get basic user info (for comments, mentions, etc.)
   */
  static async getUserBasic(
    userId: string,
    fallbackFn?: () => Promise<CachedUserBasic | null>
  ): Promise<CachedUserBasic | null> {
    try {
      const cacheKey = `${CACHE_KEYS.USER_BASIC}${userId}`;
      
      const cached = await redis.get<CachedUserBasic>(cacheKey);
      
      if (cached) {
        console.log(`📱 User basic info for ${userId} served from cache`);
        return cached;
      }
      
      if (fallbackFn) {
        console.log(`🔄 User basic info cache miss for ${userId}, fetching from database`);
        const freshData = await fallbackFn();
        
        if (freshData) {
          await this.setUserBasic(userId, freshData);
        }
        
        return freshData;
      }
      
      return null;
    } catch (error) {
      console.error(`❌ Error getting user basic info from cache for ${userId}:`, error);
      return fallbackFn ? await fallbackFn() : null;
    }
  }

  /**
   * Cache basic user info
   */
  static async setUserBasic(userId: string, userBasic: CachedUserBasic): Promise<void> {
    try {
      const cacheKey = `${CACHE_KEYS.USER_BASIC}${userId}`;
      await redis.setex(cacheKey, CACHE_TTL.USER_BASIC, userBasic);
      console.log(`✅ User basic info cached for ${userId}`);
    } catch (error) {
      console.error(`❌ Error caching user basic info for ${userId}:`, error);
    }
  }

  /**
   * Get multiple users' basic info in batch
   */
  static async getUsersBatch(
    userIds: string[],
    fallbackFn?: (missingIds: string[]) => Promise<CachedUserBasic[]>
  ): Promise<CachedUserBasic[]> {
    try {
      const cacheKeys = userIds.map(id => `${CACHE_KEYS.USER_BASIC}${id}`);
      const cached = await redis.mget<CachedUserBasic[]>(...cacheKeys);
      
      const results: CachedUserBasic[] = [];
      const missingIds: string[] = [];
      
      cached.forEach((user, index) => {
        if (user) {
          results.push(user);
          console.log(`📱 User basic info for ${userIds[index]} served from cache`);
        } else {
          missingIds.push(userIds[index]);
        }
      });
      
      if (missingIds.length > 0 && fallbackFn) {
        console.log(`🔄 Batch cache miss for ${missingIds.length} users, fetching from database`);
        const freshData = await fallbackFn(missingIds);
        
        // Cache the fresh data
        for (const user of freshData) {
          await this.setUserBasic(user.id, user);
          results.push(user);
        }
      }
      
      return results;
    } catch (error) {
      console.error(`❌ Error getting users batch from cache:`, error);
      return fallbackFn ? await fallbackFn(userIds) : [];
    }
  }

  /**
   * Get avatar URL from cache
   */
  static async getAvatarUrl(
    userId: string,
    fallbackFn?: () => Promise<string | null>
  ): Promise<string | null> {
    try {
      const cacheKey = `${CACHE_KEYS.USER_AVATAR}${userId}`;
      
      const cached = await redis.get<string>(cacheKey);
      
      if (cached !== null) {
        console.log(`📱 Avatar URL for ${userId} served from cache`);
        return cached;
      }
      
      if (fallbackFn) {
        console.log(`🔄 Avatar URL cache miss for ${userId}, fetching from database`);
        const freshData = await fallbackFn();
        
        if (freshData !== null) {
          await this.setAvatarUrl(userId, freshData);
        }
        
        return freshData;
      }
      
      return null;
    } catch (error) {
      console.error(`❌ Error getting avatar URL from cache for ${userId}:`, error);
      return fallbackFn ? await fallbackFn() : null;
    }
  }

  /**
   * Cache avatar URL
   */
  static async setAvatarUrl(userId: string, avatarUrl: string | null): Promise<void> {
    try {
      const cacheKey = `${CACHE_KEYS.USER_AVATAR}${userId}`;
      await redis.setex(cacheKey, CACHE_TTL.USER_AVATAR, avatarUrl || '');
      console.log(`✅ Avatar URL cached for ${userId}`);
    } catch (error) {
      console.error(`❌ Error caching avatar URL for ${userId}:`, error);
    }
  }

  /**
   * Invalidate user cache when profile is updated
   */
  static async invalidateUserCache(userId: string): Promise<void> {
    try {
      const keys = [
        `${CACHE_KEYS.USER_PROFILE}${userId}`,
        `${CACHE_KEYS.USER_BASIC}${userId}`,
        `${CACHE_KEYS.USER_AVATAR}${userId}`,
      ];
      
      await redis.del(...keys);
      console.log(`✅ User cache invalidated for ${userId}`);
    } catch (error) {
      console.error(`❌ Error invalidating user cache for ${userId}:`, error);
    }
  }

  /**
   * Warm up cache for frequently accessed users
   */
  static async warmUpCache(
    userIds: string[],
    fetchFn: (ids: string[]) => Promise<CachedUserBasic[]>
  ): Promise<void> {
    try {
      console.log(`🔥 Warming up cache for ${userIds.length} users`);
      const users = await fetchFn(userIds);
      
      for (const user of users) {
        await this.setUserBasic(user.id, user);
      }
      
      console.log(`✅ Cache warmed up for ${users.length} users`);
    } catch (error) {
      console.error(`❌ Error warming up user cache:`, error);
    }
  }
}
