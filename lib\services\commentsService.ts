/**
 * Optimized Comments Service with Redis Caching
 * Reduces Supabase API calls and improves performance
 */

import { supabase } from '../supabase';
import { CommentsCache } from '../redis/commentsCache';
import { UserProfileCache } from '../redis/userProfileCache';

interface Comment {
  id: string;
  content: string;
  user_id: string;
  post_id?: string;
  reel_id?: string;
  parent_comment_id: string | null;
  created_at: string;
  likes_count: number;
  replies_count?: number;
  user: {
    username: string;
    avatar: string;
  };
  replies?: Comment[];
  mentions?: Array<{ user_id: string; username: string }>;
}

interface CommentsResponse {
  comments: Comment[];
  total_count: number;
  has_more: boolean;
}

export class CommentsService {
  /**
   * Get comments with optimized caching
   */
  static async getComments(
    entityType: 'post' | 'reel',
    entityId: string,
    useCache: boolean = true
  ): Promise<Comment[]> {
    try {
      if (!useCache) {
        return await this.fetchCommentsFromDatabase(entityType, entityId);
      }

      // Try Redis cache first
      const cachedResponse = await CommentsCache.getCommentsWithUsers(
        entityType,
        entityId,
        () => this.fetchCommentsFromDatabaseWithResponse(entityType, entityId)
      );

      return cachedResponse.comments;
    } catch (error) {
      console.error(`❌ Error getting ${entityType} comments:`, error);
      // Fallback to direct database fetch
      return await this.fetchCommentsFromDatabase(entityType, entityId);
    }
  }

  /**
   * Fetch comments from database with user profile caching
   */
  private static async fetchCommentsFromDatabase(
    entityType: 'post' | 'reel',
    entityId: string
  ): Promise<Comment[]> {
    const response = await this.fetchCommentsFromDatabaseWithResponse(entityType, entityId);
    return response.comments;
  }

  /**
   * Fetch comments from database with full response
   */
  private static async fetchCommentsFromDatabaseWithResponse(
    entityType: 'post' | 'reel',
    entityId: string
  ): Promise<CommentsResponse> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }

    const table = entityType === 'reel' ? 'reel_comments' : 'comments';
    
    // First, get comments without user data
    const { data: commentsData, error } = await supabase
      .from(table)
      .select(`
        id,
        content,
        user_id,
        ${entityType}_id,
        parent_comment_id,
        created_at,
        likes_count,
        replies_count
      `)
      .eq(`${entityType}_id`, entityId)
      .order('created_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch ${entityType} comments: ${error.message}`);
    }

    if (!commentsData || commentsData.length === 0) {
      return { comments: [], total_count: 0, has_more: false };
    }

    // Extract unique user IDs
    const userIds = [...new Set(commentsData.map(comment => comment.user_id))];
    
    // Get user data from cache or database
    const users = await UserProfileCache.getUsersBatch(
      userIds,
      async (missingIds) => {
        console.log(`🔄 Fetching ${missingIds.length} user profiles from database`);
        
        const { data: usersData, error: usersError } = await supabase!
          .from('profiles')
          .select('id, username, avatar_url')
          .in('id', missingIds);

        if (usersError) {
          console.error('Error fetching user profiles:', usersError);
          return [];
        }

        return (usersData || []).map(user => ({
          id: user.id,
          username: user.username || 'Unknown',
          avatar_url: user.avatar_url,
        }));
      }
    );

    // Create user lookup map
    const userMap = new Map(users.map(user => [user.id, user]));

    // Transform comments with user data
    const commentsWithUsers: Comment[] = commentsData.map(comment => ({
      ...comment,
      user: {
        username: userMap.get(comment.user_id)?.username || 'Unknown',
        avatar: userMap.get(comment.user_id)?.avatar_url || 'https://via.placeholder.com/40',
      },
    }));

    // Nest comments (replies)
    const nestedComments = this.nestComments(commentsWithUsers);

    return {
      comments: nestedComments,
      total_count: nestedComments.length,
      has_more: false,
    };
  }

  /**
   * Get user profile with caching for comments
   */
  static async getUserForComment(userId: string): Promise<{ username: string; avatar: string }> {
    try {
      const cachedUser = await UserProfileCache.getUserBasic(
        userId,
        async () => {
          if (!supabase) return null;
          
          const { data, error } = await supabase
            .from('profiles')
            .select('id, username, avatar_url')
            .eq('id', userId)
            .single();

          if (error || !data) {
            console.error(`Error fetching user ${userId}:`, error);
            return null;
          }

          return {
            id: data.id,
            username: data.username || 'Unknown',
            avatar_url: data.avatar_url,
          };
        }
      );

      return {
        username: cachedUser?.username || 'Unknown',
        avatar: cachedUser?.avatar_url || 'https://via.placeholder.com/40',
      };
    } catch (error) {
      console.error(`Error getting user for comment ${userId}:`, error);
      return {
        username: 'Unknown',
        avatar: 'https://via.placeholder.com/40',
      };
    }
  }

  /**
   * Invalidate comments cache when new comment is added
   */
  static async invalidateCommentsCache(entityType: 'post' | 'reel', entityId: string): Promise<void> {
    try {
      await CommentsCache.invalidateCommentsCache(entityType, entityId);
      console.log(`✅ Comments cache invalidated for ${entityType} ${entityId}`);
    } catch (error) {
      console.error(`❌ Error invalidating comments cache:`, error);
    }
  }

  /**
   * Nest comments to create reply structure
   */
  private static nestComments(comments: Comment[]): Comment[] {
    const commentMap = new Map<string, Comment>();
    const rootComments: Comment[] = [];

    // First pass: create map and initialize replies array
    comments.forEach(comment => {
      comment.replies = [];
      commentMap.set(comment.id, comment);
    });

    // Second pass: nest replies
    comments.forEach(comment => {
      if (comment.parent_comment_id) {
        const parent = commentMap.get(comment.parent_comment_id);
        if (parent) {
          parent.replies!.push(comment);
        } else {
          // Parent not found, treat as root comment
          rootComments.push(comment);
        }
      } else {
        rootComments.push(comment);
      }
    });

    return rootComments;
  }

  /**
   * Warm up cache for active posts/reels
   */
  static async warmUpCommentsCache(
    entities: Array<{ type: 'post' | 'reel'; id: string }>
  ): Promise<void> {
    try {
      console.log(`🔥 Warming up comments cache for ${entities.length} entities`);
      
      for (const entity of entities) {
        await this.getComments(entity.type, entity.id, true);
      }
      
      console.log(`✅ Comments cache warmed up for ${entities.length} entities`);
    } catch (error) {
      console.error(`❌ Error warming up comments cache:`, error);
    }
  }

  /**
   * Get cache statistics
   */
  static async getCacheStats(): Promise<{
    commentsHits: number;
    commentsMisses: number;
    userHits: number;
    userMisses: number;
  }> {
    // This would require implementing cache hit/miss tracking
    // For now, return placeholder values
    return {
      commentsHits: 0,
      commentsMisses: 0,
      userHits: 0,
      userMisses: 0,
    };
  }
}
